const chatService = require('../services/chatService');

class ChatController {
  async chat(req, res) {
    try {
      const { message, threadId } = req.body;

      if (!message) {
        return res.status(400).json({ error: 'Message is required' });
      }

      // Get userId from authenticated user (set by auth middleware)
      const userId = req.user.user_id;

      const result = await chatService.processChat(message, threadId, userId);
      res.json(result);

    } catch (error) {
      console.error('Chat error:', error);
      res.status(500).json({ 
        error: 'Internal server error',
        message: error.message 
      });
    }
  }

  async getThreadMessages(req, res) {
    try {
      const { threadId } = req.params;
      const messages = await chatService.getThreadMessages(threadId);
      
      res.json({ messages });
    } catch (error) {
      console.error('Get messages error:', error);
      res.status(500).json({ 
        error: 'Failed to get messages',
        message: error.message 
      });
    }
  }

  async healthCheck(req, res) {
    res.json({ status: 'OK', message: 'Backend server is running' });
  }
}

module.exports = new ChatController();