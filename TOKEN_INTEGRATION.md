# Token Integration Documentation

## Overview
Implementasi sistem token authentication untuk AI Trainer Hub yang menghubungkan frontend (Next.js) dengan backend (Express.js) menggunakan database MySQL.

## Backend Configuration

### Database Setup
- **Database**: `aitrainerhub`
- **User**: `root`
- **Password**: `emptytable`
- **Table**: `user` dengan kolom:
  - `user_id` (INT, AUTO_INCREMENT, PRIMARY KEY)
  - `token` (VARCHAR(255), UNIQUE, NOT NULL)
  - `nama` (VARCHAR(255), NOT NULL)
  - `created_at` (TIMESTAMP)
  - `updated_at` (TIMESTAMP)

### Environment Variables (be/.env)
```
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=emptytable
DB_NAME=aitrainerhub
```

### Authentication Middleware
- File: `be/src/middleware/auth.js`
- Memvalidasi token dari header `Authorization: Bearer <token>` atau `token`
- Menambahkan user info ke `req.user` jika token valid

### Database Initialization
- File: `be/src/utils/initDatabase.js`
- Otomatis membuat tabel `user` jika belum ada
- Menambahkan sample user dengan token `abcde`

## Frontend Configuration

### Environment Variables (fe/.env.local)
```
NEXT_PUBLIC_API_URL=http://localhost:3001
NEXT_PUBLIC_TOKEN=abcde
```

### API Utility
- File: `fe/src/lib/api.ts`
- Otomatis menambahkan token ke semua API calls
- Menggunakan `Authorization: Bearer <token>` header
- Menyediakan fungsi-fungsi API yang siap pakai

### Usage Example
```typescript
import { api } from '@/lib/api';

// Semua calls otomatis menggunakan token
const response = await api.chat('Hello world');
const thread = await api.createThread();
const messages = await api.getMessages(threadId);
```

## API Endpoints

### Public Endpoints (No Token Required)
- `GET /health` - Health check

### Protected Endpoints (Token Required)
- `POST /chat` - Chat dengan AI
- `POST /assistant/thread` - Buat thread baru
- `POST /assistant/message` - Kirim message ke thread
- `POST /assistant/run` - Jalankan assistant
- `GET /assistant/messages/:threadId` - Ambil messages dari thread

## Testing

### Manual Testing
1. Start backend: `cd be && npm run dev`
2. Start frontend: `cd fe && npm run dev`
3. Buka http://localhost:3000
4. Test API calls menggunakan komponen ApiExample

### Sample User
- **Token**: `abcde`
- **Name**: `Test User`
- **User ID**: Auto-generated

## Security Notes
- Token disimpan di environment variable (tidak di code)
- Semua API calls (kecuali health check) memerlukan token valid
- Token divalidasi terhadap database setiap request
- Error handling untuk token invalid/missing

## File Structure
```
be/
├── src/
│   ├── config/
│   │   ├── database.js          # Database connection
│   │   └── init-db.sql         # SQL schema
│   ├── middleware/
│   │   └── auth.js             # Token authentication
│   ├── utils/
│   │   └── initDatabase.js     # Database initialization
│   └── app.js                  # Main app with auth middleware

fe/
├── src/
│   ├── lib/
│   │   └── api.ts              # API utility with token
│   └── components/
│       └── ApiExample.tsx      # Example usage
└── .env.local                  # Environment variables
```