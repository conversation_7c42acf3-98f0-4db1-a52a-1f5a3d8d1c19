const openai = require('../config/openai');

class ChatService {
  // Clean response by removing citation patterns like 【1:1†source†L1-L5】
  cleanResponse(response) {
    return response.replace(/【\d+:\d+†.*?†L\d+-L\d+】/g, '');
  }

  async createThread() {
    const thread = await openai.beta.threads.create();
    return thread.id;
  }

  async addMessageToThread(threadId, message) {
    await openai.beta.threads.messages.create(threadId, {
      role: 'user',
      content: message,
    });
  }

  async runAssistant(threadId) {
    const run = await openai.beta.threads.runs.create(threadId, {
      assistant_id: process.env.ASSISTANT_ID,
    });
    return run;
  }

  async waitForCompletion(threadId, runId) {
    let runStatus = await openai.beta.threads.runs.retrieve(threadId, runId);
    
    while (runStatus.status === 'queued' || runStatus.status === 'in_progress') {
      await new Promise(resolve => setTimeout(resolve, 1000));
      runStatus = await openai.beta.threads.runs.retrieve(threadId, runId);
    }
    
    return runStatus;
  }

  async getLastMessage(threadId) {
    const messages = await openai.beta.threads.messages.list(threadId);
    return messages.data[0];
  }

  async getThreadMessages(threadId, userId) {
    // TODO: Add user validation logic here
    // For now, we'll just log the userId for tracking
    console.log(`Getting messages for thread ${threadId} by user ${userId}`);
    
    const messages = await openai.beta.threads.messages.list(threadId);
    return messages.data.reverse().map(msg => ({
      id: msg.id,
      role: msg.role,
      content: msg.role === 'assistant' ? this.cleanResponse(msg.content[0].text.value) : msg.content[0].text.value,
      created_at: msg.created_at
    }));
  }

  async processChat(message, threadId = null) {
    try {
      // Create thread if not provided
      let currentThreadId = threadId;
      if (!currentThreadId) {
        currentThreadId = await this.createThread();
      }

      // Add message to thread
      await this.addMessageToThread(currentThreadId, message);

      // Run the assistant
      const run = await this.runAssistant(currentThreadId);

      // Wait for completion
      const runStatus = await this.waitForCompletion(currentThreadId, run.id);

      if (runStatus.status === 'completed') {
        const lastMessage = await this.getLastMessage(currentThreadId);
        const rawResponse = lastMessage.content[0].text.value;
        const cleanedResponse = this.cleanResponse(rawResponse);
        
        return {
          response: cleanedResponse,
          threadId: currentThreadId,
          status: 'success',
          usage: {
            prompt_tokens: runStatus.usage?.prompt_tokens || 0,
            completion_tokens: runStatus.usage?.completion_tokens || 0,
            total_tokens: runStatus.usage?.total_tokens || 0
          }
        };
      } else {
        throw new Error(`Assistant run failed with status: ${runStatus.status}`);
      }
    } catch (error) {
      throw error;
    }
  }
}

module.exports = new ChatService();