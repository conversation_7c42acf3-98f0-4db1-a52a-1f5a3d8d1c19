# AI Expert Management Feature

## Overview
This feature allows users to create and manage AI experts through a web interface. Each expert is backed by an OpenAI Assistant and stored in the database with user-specific access control.

## Components

### Backend Components

1. **Database Schema** (`be/src/config/init-db.sql`)
   - `experts` table with fields: id, user_id, name, description, system_prompt, model, assistant_id, pricing_percentage, timestamps
   - Foreign key relationship with users table

2. **Expert Service** (`be/src/services/expertService.js`)
   - `createExpert()` - Creates OpenAI assistant and saves to database
   - `listUserExperts()` - Lists all experts for a user
   - `getExpert()` - Gets specific expert details
   - Validation methods for expert data

3. **Expert Controller** (`be/src/controllers/expertController.js`)
   - Handles HTTP requests for expert operations
   - Validates user authentication and input data
   - Returns JSON responses

4. **Expert Routes** (`be/src/routes/expertRoutes.js`)
   - `POST /api/experts` - Create new expert (with optional file upload)
   - `GET /api/experts` - List user's experts
   - `GET /api/experts/:expertId` - Get specific expert

### Frontend Components

1. **CreateExpert Component** (`fe/src/components/CreateExpert.tsx`)
   - Form for creating new AI experts
   - Fields: name, description, system prompt, model selection, pricing, file upload
   - File validation and upload handling
   - Form validation and error handling

2. **ExpertList Component** (`fe/src/components/ExpertList.tsx`)
   - Displays list of user's experts
   - Shows expert details: name, description, model, assistant ID, pricing
   - Click to select expert functionality
   - Loading and error states

3. **ExpertPanel Component** (`fe/src/components/ExpertPanel.tsx`)
   - Main container with tab navigation
   - Switches between "My Experts" and "Create Expert" views
   - Handles expert creation success and list refresh

4. **Navigation Component** (`fe/src/components/Navigation.tsx`)
   - Site-wide navigation with active state highlighting
   - Links to Home and AI Experts pages

5. **Experts Page** (`fe/src/app/experts/page.tsx`)
   - Main page for expert management
   - Renders ExpertPanel component

### API Integration

Updated `fe/src/lib/api.ts` with expert-specific functions:
- `createExpert(formData)` - Handles FormData for file uploads
- `listExperts()` - Gets user's experts list
- `getExpert(expertId)` - Gets specific expert details

## Features

### Expert Creation
- Name and description fields
- System prompt definition (required)
- Model selection (GPT-3.5, GPT-4 variants)
- Optional file upload for knowledge base
- Pricing percentage configuration
- Creates OpenAI Assistant automatically
- Stores expert data in database

### Expert Management
- List all user's experts
- View expert details including Assistant ID
- Search and filter capabilities (can be added)
- Edit/delete functionality (can be added)

### File Upload Support
- PDF, TXT, DOCX, DOC, MD, JSON files
- Automatic vector store creation for file search
- File validation and error handling

### Authentication & Security
- Token-based authentication
- User-specific expert access
- Input validation and sanitization
- Error handling and logging

## Usage

1. **Navigate to Experts Page**: Click "AI Experts" in navigation
2. **Create Expert**: 
   - Click "Create Expert" tab
   - Fill in required fields (name, system prompt)
   - Optionally add description, select model, set pricing
   - Upload file if needed for knowledge base
   - Click "Create Expert"
3. **View Experts**: 
   - Click "My Experts" tab
   - See list of created experts
   - Click on expert for details

## Database Setup

The experts table is automatically created when the server starts. Make sure your database connection is configured in `be/.env`:

```
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=aitrainerhub
```

## Testing

Use `test-expert-creation.js` to test the expert creation API:

```bash
node test-expert-creation.js
```

## Future Enhancements

- Expert editing functionality
- Expert deletion with cleanup
- Expert sharing between users
- Usage analytics and monitoring
- Advanced file processing options
- Expert templates and presets
- Bulk operations
- Export/import functionality