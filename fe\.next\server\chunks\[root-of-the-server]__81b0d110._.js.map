{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/ai%20trainerhub/fe/src/app/api/chat/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\nconst API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';\nconst TOKEN = process.env.NEXT_PUBLIC_TOKEN || 'abcde';\n\nexport async function POST(req: NextRequest) {\n  try {\n    const { threadId, message } = await req.json();\n\n    if (!message) {\n      return new NextResponse('Missing message', { status: 400 });\n    }\n\n    // Proxy request to backend with token (userId will be extracted from token by auth middleware)\n    const response = await fetch(`${API_URL}/api/chat`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${TOKEN}`,\n      },\n      body: JSON.stringify({\n        threadId,\n        message,\n      }),\n    });\n\n    if (!response.ok) {\n      const error = await response.json();\n      return new NextResponse(JSON.stringify(error), {\n        status: response.status,\n        headers: { 'Content-Type': 'application/json' },\n      });\n    }\n\n    const data = await response.json();\n    return NextResponse.json(data);\n  } catch (error) {\n    console.error('Chat API error:', error);\n    return new NextResponse('Internal Server Error', { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,UAAU,6DAAmC;AACnD,MAAM,QAAQ,6CAAiC;AAExC,eAAe,KAAK,GAAgB;IACzC,IAAI;QACF,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,MAAM,IAAI,IAAI;QAE5C,IAAI,CAAC,SAAS;YACZ,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,mBAAmB;gBAAE,QAAQ;YAAI;QAC3D;QAEA,+FAA+F;QAC/F,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,SAAS,CAAC,EAAE;YAClD,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;YACpC;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB;gBACA;YACF;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;YACjC,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,KAAK,SAAS,CAAC,QAAQ;gBAC7C,QAAQ,SAAS,MAAM;gBACvB,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;QACF;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mBAAmB;QACjC,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,yBAAyB;YAAE,QAAQ;QAAI;IACjE;AACF", "debugId": null}}]}