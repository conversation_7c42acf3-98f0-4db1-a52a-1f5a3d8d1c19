// Test script untuk memverifikasi koneksi langsung ke backend dengan token
const API_URL = 'http://localhost:3001';
const TOKEN = 'abcde';

async function testDirectBackend() {
  console.log('🚀 Testing Direct Backend Connection with Token...\n');

  try {
    // Test 1: Health check (no token required)
    console.log('1. Testing health check...');
    const healthResponse = await fetch(`${API_URL}/health`);
    const healthData = await healthResponse.json();
    console.log('✅ Health check:', healthData);
    console.log('');

    // Test 2: Chat API with token
    console.log('2. Testing chat API with token...');
    const chatResponse = await fetch(`${API_URL}/api/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${TOKEN}`
      },
      body: JSON.stringify({
        message: 'Hello from frontend test',
        threadId: null
      })
    });

    if (chatResponse.ok) {
      const chatData = await chatResponse.json();
      console.log('✅ Chat response:', chatData);
    } else {
      const errorData = await chatResponse.json();
      console.log('❌ Chat failed:', errorData);
    }
    console.log('');

    // Test 3: Assistant thread creation
    console.log('3. Testing assistant thread creation...');
    const threadResponse = await fetch(`${API_URL}/assistant/thread`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${TOKEN}`
      }
    });

    if (threadResponse.ok) {
      const threadData = await threadResponse.json();
      console.log('✅ Thread created:', threadData);
    } else {
      const errorData = await threadResponse.json();
      console.log('❌ Thread creation failed:', errorData);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.log('\n💡 Make sure backend is running: cd be && npm run dev');
  }
}

// Export untuk digunakan di browser console juga
if (typeof window !== 'undefined') {
  window.testDirectBackend = testDirectBackend;
}

// Run jika dipanggil langsung
if (require.main === module) {
  testDirectBackend();
}

module.exports = { testDirectBackend };