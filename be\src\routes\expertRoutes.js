const express = require('express');
const expertController = require('../controllers/expertController');
const { upload, handleUploadError } = require('../middleware/upload');
const auth = require('../middleware/auth');

const router = express.Router();

// Create expert endpoint with optional file upload
router.post('/api/experts', auth, upload, handleUploadError, expertController.createExpert);

// List all experts for user
router.get('/api/experts', auth, expertController.listExperts);

// Get specific expert
router.get('/api/experts/:expertId', auth, expertController.getExpert);

module.exports = router;