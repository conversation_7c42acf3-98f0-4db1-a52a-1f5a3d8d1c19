# Troubleshooting Guide - Token Integration

## Problem: Frontend calling wrong URL (localhost:3000 instead of localhost:3001)

### Symptoms:
- Error 404 when calling `/api/chat`
- Browser network tab shows requests to `http://localhost:3000/api/chat`
- Should be calling `http://localhost:3001/api/chat` (backend)

### Solutions:

#### 1. Check Environment Variables
Pastikan file `fe/.env.local` berisi:
```
NEXT_PUBLIC_API_URL=http://localhost:3001
NEXT_PUBLIC_TOKEN=abcde
```

#### 2. Restart Frontend Development Server
Environment variables hanya dimuat saat startup:
```bash
cd fe
# Stop server (Ctrl+C)
npm run dev
```

#### 3. Clear Browser Cache
- Hard refresh: Ctrl+F5 atau Ctrl+Shift+R
- Clear browser cache dan cookies untuk localhost
- Atau buka Incognito/Private window

#### 4. Check Browser Console
Buka Developer Tools (F12) dan lihat:
- Console logs untuk debug info dari API calls
- Network tab untuk melihat actual URLs yang dipanggil
- Jalankan `testApi()` di console untuk test langsung

#### 5. Verify Backend is Running
```bash
cd be
npm run dev
```
Should show:
```
Backend server running on port 3001
Frontend URL: http://localhost:3000
Database connected successfully
Database initialized successfully
```

#### 6. Test Direct Backend Connection
Buka browser dan test:
- `http://localhost:3001/health` - Should return `{"status":"OK","message":"Server is running"}`
- Use Postman/curl to test chat endpoint:
```bash
curl -X POST http://localhost:3001/api/chat \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer abcde" \
  -d '{"message":"test","threadId":null}'
```

#### 7. Check Database Connection
Pastikan MySQL running dan database `aitrainerhub` exists:
```sql
-- Connect to MySQL
mysql -u root -p

-- Check database
SHOW DATABASES;
USE aitrainerhub;
SHOW TABLES;
SELECT * FROM user;
```

#### 8. CORS Issues
Jika ada CORS error, pastikan backend CORS middleware configured untuk frontend URL.

### Debug Steps:

1. **Check API Configuration**:
   ```javascript
   // In browser console
   console.log('API_URL:', process.env.NEXT_PUBLIC_API_URL);
   console.log('TOKEN:', process.env.NEXT_PUBLIC_TOKEN);
   ```

2. **Test API Utility**:
   ```javascript
   // In browser console (after loading page)
   testApi();
   ```

3. **Manual Fetch Test**:
   ```javascript
   // In browser console
   fetch('http://localhost:3001/health')
     .then(r => r.json())
     .then(console.log);
   ```

### Common Issues:

1. **Environment variables not loaded**: Restart dev server
2. **Backend not running**: Start with `cd be && npm run dev`
3. **Database not connected**: Check MySQL service and credentials
4. **Wrong port**: Frontend=3000, Backend=3001
5. **Token missing**: Check NEXT_PUBLIC_TOKEN in .env.local
6. **CORS blocked**: Check backend CORS configuration

### File Locations:
- Frontend env: `fe/.env.local`
- Backend env: `be/.env`
- API utility: `fe/src/lib/api.ts`
- Test utility: `fe/public/test-api.js`
- Auth middleware: `be/src/middleware/auth.js`