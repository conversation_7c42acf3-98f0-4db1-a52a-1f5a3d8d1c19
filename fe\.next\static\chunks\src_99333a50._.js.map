{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/ai%20trainerhub/fe/src/app/client-provider.tsx"], "sourcesContent": ["\"use client\";\n\nimport { QueryClient, QueryClientProvider } from \"@tanstack/react-query\";\nimport { useState } from \"react\";\n\nexport default function ClientProvider({ children }: { children: React.ReactNode }) {\n  const [queryClient] = useState(() => new QueryClient());\n  \n  return (\n    <QueryClientProvider client={queryClient}>\n      {children}\n    </QueryClientProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;;;AAHA;;;AAKe,SAAS,eAAe,KAA2C;QAA3C,EAAE,QAAQ,EAAiC,GAA3C;;IACrC,MAAM,CAAC,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;mCAAE,IAAM,IAAI,gLAAA,CAAA,cAAW;;IAEpD,qBACE,6LAAC,yLAAA,CAAA,sBAAmB;QAAC,QAAQ;kBAC1B;;;;;;AAGP;GARwB;KAAA", "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/ai%20trainerhub/fe/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport Link from 'next/link';\r\nimport { usePathname } from 'next/navigation';\r\n\r\nconst Navigation = () => {\r\n  const pathname = usePathname();\r\n\r\n  const navItems = [\r\n    { href: '/', label: 'Home' },\r\n    { href: '/experts', label: 'AI Experts' }\r\n  ];\r\n\r\n  return (\r\n    <nav className=\"bg-white shadow-sm border-b\">\r\n      <div className=\"max-w-6xl mx-auto px-4\">\r\n        <div className=\"flex justify-between items-center h-16\">\r\n          <div className=\"flex items-center space-x-8\">\r\n            <Link href=\"/\" className=\"text-xl font-bold text-blue-600\">\r\n              AI Trainer Hub\r\n            </Link>\r\n            \r\n            <div className=\"flex space-x-6\">\r\n              {navItems.map((item) => (\r\n                <Link\r\n                  key={item.href}\r\n                  href={item.href}\r\n                  className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${\r\n                    pathname === item.href\r\n                      ? 'bg-blue-100 text-blue-700'\r\n                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'\r\n                  }`}\r\n                >\r\n                  {item.label}\r\n                </Link>\r\n              ))}\r\n            </div>\r\n          </div>\r\n          \r\n          <div className=\"text-sm text-gray-500\">\r\n            Token: abcde\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nav>\r\n  );\r\n};\r\n\r\nexport default Navigation;"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKA,MAAM,aAAa;;IACjB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,WAAW;QACf;YAAE,MAAM;YAAK,OAAO;QAAO;QAC3B;YAAE,MAAM;YAAY,OAAO;QAAa;KACzC;IAED,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CAAkC;;;;;;0CAI3D,6LAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,+JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAW,AAAC,8DAIX,OAHC,aAAa,KAAK,IAAI,GAClB,8BACA;kDAGL,KAAK,KAAK;uCARN,KAAK,IAAI;;;;;;;;;;;;;;;;kCActB,6LAAC;wBAAI,WAAU;kCAAwB;;;;;;;;;;;;;;;;;;;;;;AAOjD;GAzCM;;QACa,qIAAA,CAAA,cAAW;;;KADxB;uCA2CS", "debugId": null}}]}