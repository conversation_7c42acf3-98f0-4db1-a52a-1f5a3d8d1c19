const { pool } = require('../config/database');
const assistantService = require('./assistantService');

class ExpertService {
  async createExpert(expertData, file = null) {
    try {
      const { userId, name, description, systemPrompt, model, pricingPercentage } = expertData;

      // Create OpenAI assistant
      const assistantResult = await assistantService.processAssistantCreation({
        name,
        instructions: systemPrompt,
        model: model || 'gpt-4o-mini',
        userId
      }, file);

      if (!assistantResult.success) {
        throw new Error('Failed to create OpenAI assistant');
      }

      // Save expert to database
      const query = `
        INSERT INTO experts (user_id, name, description, system_prompt, model, assistant_id, pricing_percentage)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `;

      const values = [
        userId,
        name,
        description || '',
        systemPrompt,
        model || 'gpt-4o-mini',
        assistantResult.assistant.id,
        pricingPercentage || 0.00
      ];

      const [result] = await pool.execute(query, values);

      return {
        success: true,
        expert: {
          id: result.insertId,
          userId,
          name,
          description,
          systemPrompt,
          model: model || 'gpt-4o-mini',
          assistantId: assistantResult.assistant.id,
          pricingPercentage: pricingPercentage || 0.00,
          createdAt: new Date()
        }
      };
    } catch (error) {
      console.error('Expert creation error:', error);
      throw error;
    }
  }

  async listUserExperts(userId) {
    try {
      const query = `
        SELECT id, name, description, system_prompt, model, assistant_id, 
               pricing_percentage, created_at, updated_at
        FROM experts 
        WHERE user_id = ?
        ORDER BY created_at DESC
      `;

      const [rows] = await pool.execute(query, [userId]);

      return {
        success: true,
        experts: rows.map(row => ({
          id: row.id,
          name: row.name,
          description: row.description,
          systemPrompt: row.system_prompt,
          model: row.model,
          assistantId: row.assistant_id,
          pricingPercentage: row.pricing_percentage,
          createdAt: row.created_at,
          updatedAt: row.updated_at
        }))
      };
    } catch (error) {
      console.error('List experts error:', error);
      throw error;
    }
  }

  async getExpert(expertId, userId) {
    try {
      const query = `
        SELECT id, name, description, system_prompt, model, assistant_id, 
               pricing_percentage, created_at, updated_at
        FROM experts 
        WHERE id = ? AND user_id = ?
      `;

      const [rows] = await pool.execute(query, [expertId, userId]);

      if (rows.length === 0) {
        return {
          success: false,
          error: 'Expert not found'
        };
      }

      const expert = rows[0];
      return {
        success: true,
        expert: {
          id: expert.id,
          name: expert.name,
          description: expert.description,
          systemPrompt: expert.system_prompt,
          model: expert.model,
          assistantId: expert.assistant_id,
          pricingPercentage: expert.pricing_percentage,
          createdAt: expert.created_at,
          updatedAt: expert.updated_at
        }
      };
    } catch (error) {
      console.error('Get expert error:', error);
      throw error;
    }
  }

  validateExpertData(data) {
    const errors = [];

    if (!data.name || data.name.trim() === '') {
      errors.push('Name is required');
    }

    if (!data.systemPrompt || data.systemPrompt.trim() === '') {
      errors.push('System prompt is required');
    }

    if (data.model && !this.isValidModel(data.model)) {
      errors.push('Invalid model specified');
    }

    if (data.pricingPercentage && (isNaN(data.pricingPercentage) || data.pricingPercentage < 0 || data.pricingPercentage > 100)) {
      errors.push('Pricing percentage must be between 0 and 100');
    }

    return errors;
  }

  isValidModel(model) {
    const validModels = [
      'gpt-3.5-turbo',
      'gpt-4',
      'gpt-4-turbo',
      'gpt-4-turbo-preview',
      'gpt-4o',
      'gpt-4o-mini'
    ];
    return validModels.includes(model);
  }
}

module.exports = new ExpertService();