const expertService = require('../services/expertService');

class ExpertController {
  async createExpert(req, res) {
    try {
      const { name, description, systemPrompt, model, pricingPercentage } = req.body;
      const userId = req.user?.user_id;
      const file = req.file;

      if (!userId) {
        return res.status(400).json({
          success: false,
          error: 'User ID is required'
        });
      }

      // Validate required fields
      const validationErrors = expertService.validateExpertData({
        name,
        systemPrompt,
        model,
        pricingPercentage
      });

      if (validationErrors.length > 0) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: validationErrors
        });
      }

      // Process expert creation
      const result = await expertService.createExpert({
        userId,
        name: name.trim(),
        description: description?.trim() || '',
        systemPrompt: systemPrompt.trim(),
        model: model || 'gpt-4o-mini',
        pricingPercentage: parseFloat(pricingPercentage) || 0.00
      }, file);

      res.json(result);

    } catch (error) {
      console.error('Create expert error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: error.message
      });
    }
  }

  async listExperts(req, res) {
    try {
      const userId = req.user?.user_id;

      if (!userId) {
        return res.status(400).json({
          success: false,
          error: 'User ID is required'
        });
      }

      const result = await expertService.listUserExperts(userId);
      res.json(result);
    } catch (error) {
      console.error('List experts error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to list experts',
        message: error.message
      });
    }
  }

  async getExpert(req, res) {
    try {
      const { expertId } = req.params;
      const userId = req.user?.user_id;

      if (!userId) {
        return res.status(400).json({
          success: false,
          error: 'User ID is required'
        });
      }

      const result = await expertService.getExpert(expertId, userId);
      
      if (!result.success) {
        return res.status(404).json(result);
      }

      res.json(result);
    } catch (error) {
      console.error('Get expert error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get expert',
        message: error.message
      });
    }
  }
}

module.exports = new ExpertController();