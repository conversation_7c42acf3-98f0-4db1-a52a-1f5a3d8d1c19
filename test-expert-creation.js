const fetch = require('node-fetch');

const API_URL = 'http://localhost:3001';
const TOKEN = 'abcde';

async function testExpertCreation() {
  try {
    console.log('🧪 Testing Expert Creation...');

    // Test data
    const expertData = {
      name: 'Test Creative Writer',
      description: 'A creative writing assistant that helps with storytelling and content creation.',
      systemPrompt: 'You are a creative writing assistant. Help users with storytelling, character development, and creative content creation. Be imaginative and inspiring.',
      model: 'gpt-4o-mini',
      pricingPercentage: 5.00
    };

    console.log('📤 Sending expert creation request...');
    console.log('Data:', expertData);

    const response = await fetch(`${API_URL}/api/experts`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${TOKEN}`
      },
      body: JSON.stringify(expertData)
    });

    console.log('📡 Response status:', response.status);

    const result = await response.json();
    console.log('📋 Response data:', JSON.stringify(result, null, 2));

    if (result.success) {
      console.log('✅ Expert created successfully!');
      console.log('Expert ID:', result.expert.id);
      console.log('Assistant ID:', result.expert.assistantId);

      // Test listing experts
      console.log('\n🔍 Testing expert listing...');
      const listResponse = await fetch(`${API_URL}/api/experts`, {
        headers: {
          'Authorization': `Bearer ${TOKEN}`
        }
      });

      const listResult = await listResponse.json();
      console.log('📋 Experts list:', JSON.stringify(listResult, null, 2));

    } else {
      console.log('❌ Expert creation failed:', result.error);
      if (result.details) {
        console.log('Details:', result.details);
      }
    }

  } catch (error) {
    console.error('💥 Test failed:', error.message);
  }
}

// Run the test
testExpertCreation();