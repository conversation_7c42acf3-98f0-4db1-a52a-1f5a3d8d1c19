const axios = require('axios');

const API_URL = 'http://localhost:3001';
const TOKEN = 'abcde';

async function testTokenIntegration() {
  console.log('🚀 Testing Token Integration...\n');

  try {
    // Test 1: Health check (no token required)
    console.log('1. Testing health check (no token required)...');
    const healthResponse = await axios.get(`${API_URL}/health`);
    console.log('✅ Health check:', healthResponse.data);
    console.log('');

    // Test 2: API call without token (should fail)
    console.log('2. Testing API call without token (should fail)...');
    try {
      await axios.post(`${API_URL}/chat`, { message: 'Hello' });
      console.log('❌ This should have failed!');
    } catch (error) {
      console.log('✅ Correctly rejected:', error.response?.data?.error || error.message);
    }
    console.log('');

    // Test 3: API call with invalid token (should fail)
    console.log('3. Testing API call with invalid token (should fail)...');
    try {
      await axios.post(`${API_URL}/chat`, 
        { message: 'Hello' },
        { headers: { 'Authorization': 'Bearer invalid-token' } }
      );
      console.log('❌ This should have failed!');
    } catch (error) {
      console.log('✅ Correctly rejected:', error.response?.data?.error || error.message);
    }
    console.log('');

    // Test 4: API call with valid token (should succeed)
    console.log('4. Testing API call with valid token (should succeed)...');
    try {
      const chatResponse = await axios.post(`${API_URL}/chat`, 
        { message: 'Hello, this is a test message' },
        { headers: { 'Authorization': `Bearer ${TOKEN}` } }
      );
      console.log('✅ Chat response received:', chatResponse.data);
    } catch (error) {
      console.log('❌ Failed:', error.response?.data || error.message);
    }
    console.log('');

    // Test 5: Assistant API with token
    console.log('5. Testing Assistant API with token...');
    try {
      const threadResponse = await axios.post(`${API_URL}/assistant/thread`, 
        {},
        { headers: { 'Authorization': `Bearer ${TOKEN}` } }
      );
      console.log('✅ Thread created:', threadResponse.data);
    } catch (error) {
      console.log('❌ Failed:', error.response?.data || error.message);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
if (require.main === module) {
  testTokenIntegration();
}

module.exports = { testTokenIntegration };