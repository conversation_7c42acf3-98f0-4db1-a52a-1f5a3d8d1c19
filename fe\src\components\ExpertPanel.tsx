'use client';

import React, { useState } from 'react';
import C<PERSON><PERSON>x<PERSON> from './CreateExpert';
import ExpertList from './ExpertList';

const ExpertPanel: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'list' | 'create'>('list');
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const handleExpertCreated = (expert: any) => {
    console.log('Expert created:', expert);
    setRefreshTrigger(prev => prev + 1);
    setActiveTab('list');
  };

  const handleExpertSelect = (expert: any) => {
    console.log('Expert selected:', expert);
    // You can add navigation or modal logic here
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Tab Navigation */}
      <div className="flex border-b border-gray-200 mb-6">
        <button
          onClick={() => setActiveTab('list')}
          className={`px-6 py-3 font-medium text-sm border-b-2 transition-colors ${
            activeTab === 'list'
              ? 'border-blue-600 text-blue-600'
              : 'border-transparent text-gray-500 hover:text-gray-700'
          }`}
        >
          My Experts
        </button>
        <button
          onClick={() => setActiveTab('create')}
          className={`px-6 py-3 font-medium text-sm border-b-2 transition-colors ${
            activeTab === 'create'
              ? 'border-blue-600 text-blue-600'
              : 'border-transparent text-gray-500 hover:text-gray-700'
          }`}
        >
          Create Expert
        </button>
      </div>

      {/* Tab Content */}
      <div className="min-h-[400px]">
        {activeTab === 'list' && (
          <ExpertList 
            onExpertSelect={handleExpertSelect}
            refreshTrigger={refreshTrigger}
          />
        )}
        
        {activeTab === 'create' && (
          <CreateExpert 
            onExpertCreated={handleExpertCreated}
            onCancel={() => setActiveTab('list')}
          />
        )}
      </div>
    </div>
  );
};

export default ExpertPanel;